'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { supabase } from '@/lib/database/hybrid-client';

interface TypingUser {
  user_id: string;
  updated_at: string;
}

/**
 * Hook for managing typing indicators
 */
export const useTypingIndicator = (conversationId: string) => {
  const { data: session } = useSession();
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const channelRef = useRef<any>(null);

  // Start typing indicator
  const startTyping = useCallback(async () => {
    if (!session?.user?.id || !conversationId || isTyping) return;

    try {
      setIsTyping(true);
      
      // Call Supabase function to set typing indicator
      await supabase.rpc('set_typing_indicator', {
        p_conversation_id: conversationId,
        p_user_id: session.user.id,
        p_is_typing: true,
      });

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Auto-stop typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping();
      }, 3000);

    } catch (error) {
      console.error('Error starting typing indicator:', error);
      setIsTyping(false);
    }
  }, [session?.user?.id, conversationId, isTyping]);

  // Stop typing indicator
  const stopTyping = useCallback(async () => {
    if (!session?.user?.id || !conversationId || !isTyping) return;

    try {
      setIsTyping(false);
      
      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
        typingTimeoutRef.current = null;
      }

      // Call Supabase function to remove typing indicator
      await supabase.rpc('set_typing_indicator', {
        p_conversation_id: conversationId,
        p_user_id: session.user.id,
        p_is_typing: false,
      });

    } catch (error) {
      console.error('Error stopping typing indicator:', error);
    }
  }, [session?.user?.id, conversationId, isTyping]);

  // Subscribe to typing indicators for this conversation
  useEffect(() => {
    if (!conversationId) return;

    const channel = supabase
      .channel(`typing-${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'typing_indicators',
          filter: `conversation_id=eq.${conversationId}`,
        },
        (payload) => {
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            const typingData = payload.new as any;
            
            // Don't show own typing indicator
            if (typingData.user_id === session?.user?.id) return;

            if (typingData.is_typing) {
              setTypingUsers(prev => {
                const filtered = prev.filter(u => u.user_id !== typingData.user_id);
                return [...filtered, {
                  user_id: typingData.user_id,
                  updated_at: typingData.updated_at,
                }];
              });
            } else {
              setTypingUsers(prev => prev.filter(u => u.user_id !== typingData.user_id));
            }
          } else if (payload.eventType === 'DELETE') {
            const deletedData = payload.old as any;
            setTypingUsers(prev => prev.filter(u => u.user_id !== deletedData.user_id));
          }
        }
      )
      .subscribe();

    channelRef.current = channel;

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [conversationId, session?.user?.id]);

  // Cleanup typing indicator on unmount
  useEffect(() => {
    return () => {
      if (isTyping) {
        stopTyping();
      }
    };
  }, [isTyping, stopTyping]);

  // Handle input change for typing detection
  const handleInputChange = useCallback((value: string) => {
    if (value.trim().length > 0) {
      startTyping();
    } else {
      stopTyping();
    }
  }, [startTyping, stopTyping]);

  return {
    typingUsers,
    isTyping,
    startTyping,
    stopTyping,
    handleInputChange,
  };
};

/**
 * Hook for managing user presence (online/offline status)
 */
export const useUserPresence = () => {
  const { data: session } = useSession();
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const presenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update user presence
  const updatePresence = useCallback(async (isOnline: boolean = true, currentPage?: string) => {
    if (!session?.user?.id) return;

    try {
      await supabase.rpc('update_user_presence', {
        p_user_id: session.user.id,
        p_is_online: isOnline,
        p_current_page: currentPage,
      });
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  }, [session?.user?.id]);

  // Set user as online
  const setOnline = useCallback((currentPage?: string) => {
    updatePresence(true, currentPage);
    
    // Update presence every 2 minutes to keep alive
    if (presenceTimeoutRef.current) {
      clearInterval(presenceTimeoutRef.current);
    }
    
    presenceTimeoutRef.current = setInterval(() => {
      updatePresence(true, currentPage);
    }, 2 * 60 * 1000); // 2 minutes
  }, [updatePresence]);

  // Set user as offline
  const setOffline = useCallback(() => {
    updatePresence(false);
    
    if (presenceTimeoutRef.current) {
      clearInterval(presenceTimeoutRef.current);
      presenceTimeoutRef.current = null;
    }
  }, [updatePresence]);

  // Subscribe to presence changes
  useEffect(() => {
    const channel = supabase
      .channel('user-presence')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
        },
        (payload) => {
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            const presenceData = payload.new as any;
            if (presenceData.is_online) {
              setOnlineUsers(prev => [...new Set([...prev, presenceData.user_id])]);
            } else {
              setOnlineUsers(prev => prev.filter(id => id !== presenceData.user_id));
            }
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Auto set online when component mounts
  useEffect(() => {
    if (session?.user?.id) {
      setOnline(window.location.pathname);
    }

    return () => {
      setOffline();
    };
  }, [session?.user?.id, setOnline, setOffline]);

  // Handle page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        setOnline(window.location.pathname);
      } else {
        setOffline();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [setOnline, setOffline]);

  return {
    onlineUsers,
    setOnline,
    setOffline,
    isUserOnline: (userId: string) => onlineUsers.includes(userId),
  };
};
