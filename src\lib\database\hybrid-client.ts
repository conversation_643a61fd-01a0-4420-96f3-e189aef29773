/**
 * Hybrid Database Client
 * Combines MySQL (main data) with Supa<PERSON> (real-time features)
 */

import { createClient } from '@supabase/supabase-js';
import { db as mysqlDb } from '../db';
import { messages, notifications } from '../db/schema';

// Supabase client for real-time features
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
);

// Server-side Supabase client (for admin operations)
export const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Export MySQL client
export { mysqlDb };

// Hybrid Database Operations
export class HybridDB {
  /**
   * Send a real-time message
   * 1. Save to MySQL (permanent storage)
   * 2. Trigger Supabase event (real-time notification)
   */
  static async sendMessage(messageData: {
    senderId: string;
    receiverId: string;
    content: string;
    conversationId?: string;
  }) {
    try {
      // 1. Save to MySQL first (permanent storage)
      const mysqlMessage = await mysqlDb.insert(messages).values({
        id: crypto.randomUUID(),
        senderId: messageData.senderId,
        receiverId: messageData.receiverId,
        content: messageData.content,
        createdAt: new Date(),
      });

      // 2. Trigger real-time event in Supabase
      const { error } = await supabaseAdmin
        .from('live_events')
        .insert({
          type: 'new_message',
          recipient_id: messageData.receiverId,
          sender_id: messageData.senderId,
          data: {
            message_id: mysqlMessage.insertId,
            content: messageData.content,
            created_at: new Date().toISOString(),
          },
        });

      if (error) {
        console.error('Supabase real-time error:', error);
        // Message still saved in MySQL, so not a critical error
      }

      return { success: true, data: mysqlMessage };
    } catch (error) {
      console.error('Hybrid message send error:', error);
      throw error;
    }
  }

  /**
   * Send a real-time notification
   * 1. Save to MySQL (permanent storage)
   * 2. Trigger Supabase event (real-time notification)
   */
  static async sendNotification(notificationData: {
    recipientId: string;
    senderId: string;
    type: string;
    postId?: string;
    commentId?: string;
    messageId?: string;
  }) {
    try {
      // 1. Save to MySQL first
      const mysqlNotification = await mysqlDb.insert(notifications).values({
        id: crypto.randomUUID(),
        recipientId: notificationData.recipientId,
        senderId: notificationData.senderId,
        type: notificationData.type,
        postId: notificationData.postId,
        commentId: notificationData.commentId,
        messageId: notificationData.messageId,
        read: false,
        createdAt: new Date(),
      });

      // 2. Trigger real-time event in Supabase
      const { error } = await supabaseAdmin
        .from('live_events')
        .insert({
          type: 'notification',
          recipient_id: notificationData.recipientId,
          sender_id: notificationData.senderId,
          data: {
            notification_id: mysqlNotification.insertId,
            notification_type: notificationData.type,
            post_id: notificationData.postId,
            created_at: new Date().toISOString(),
          },
        });

      if (error) {
        console.error('Supabase notification error:', error);
      }

      return { success: true, data: mysqlNotification };
    } catch (error) {
      console.error('Hybrid notification error:', error);
      throw error;
    }
  }

  /**
   * Get user's real-time channel name
   */
  static getUserChannel(userId: string): string {
    return `user-${userId}`;
  }

  /**
   * Get conversation's real-time channel name
   */
  static getConversationChannel(conversationId: string): string {
    return `conversation-${conversationId}`;
  }

  /**
   * Subscribe to user's real-time events
   */
  static subscribeToUserEvents(
    userId: string,
    onMessage: (payload: any) => void,
    onNotification: (payload: any) => void
  ) {
    const channel = supabase
      .channel(this.getUserChannel(userId))
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_events',
          filter: `recipient_id=eq.${userId}`,
        },
        (payload) => {
          const event = payload.new as any;
          
          if (event.type === 'new_message') {
            onMessage(event);
          } else if (event.type === 'notification') {
            onNotification(event);
          }
        }
      )
      .subscribe();

    return channel;
  }

  /**
   * Unsubscribe from real-time events
   */
  static unsubscribe(channel: any) {
    return supabase.removeChannel(channel);
  }
}


