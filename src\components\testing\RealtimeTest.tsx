'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRealtime } from '@/hooks/useRealtime';
import { useTypingIndicator, useUserPresence } from '@/hooks/useTypingIndicator';
import { RealtimeStatus, TypingIndicator, OnlineUsersList } from '@/components/messages/RealtimeStatus';

export function RealtimeTest() {
  const { data: session } = useSession();
  const { isConnected, connectionError } = useRealtime();
  const { onlineUsers, setOnline, setOffline } = useUserPresence();
  const { typingUsers, startTyping, stopTyping } = useTypingIndicator('test-conversation');
  
  const [testMessage, setTestMessage] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // Test real-time connection
  const testConnection = () => {
    if (isConnected) {
      addTestResult('✅ Real-time connection is active');
    } else if (connectionError) {
      addTestResult(`❌ Connection error: ${connectionError}`);
    } else {
      addTestResult('⏳ Connecting to real-time...');
    }
  };

  // Test message sending
  const testMessageSend = async () => {
    if (!testMessage.trim()) {
      addTestResult('❌ Please enter a test message');
      return;
    }

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          receiverId: session?.user?.id, // Send to self for testing
          content: testMessage,
        }),
      });

      if (response.ok) {
        addTestResult('✅ Test message sent successfully');
        setTestMessage('');
      } else {
        addTestResult('❌ Failed to send test message');
      }
    } catch (error) {
      addTestResult(`❌ Error sending message: ${error}`);
    }
  };

  // Test notification sending
  const testNotification = async () => {
    try {
      // Create a test post first, then like it to trigger notification
      const postResponse = await fetch('/api/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: 'Test post for real-time notification',
        }),
      });

      if (postResponse.ok) {
        const postData = await postResponse.json();
        
        // Like the post to trigger notification
        const likeResponse = await fetch(`/api/posts/${postData.id}/like`, {
          method: 'POST',
        });

        if (likeResponse.ok) {
          addTestResult('✅ Test notification triggered');
        } else {
          addTestResult('❌ Failed to trigger notification');
        }
      } else {
        addTestResult('❌ Failed to create test post');
      }
    } catch (error) {
      addTestResult(`❌ Error testing notification: ${error}`);
    }
  };

  // Test typing indicator
  const testTyping = () => {
    startTyping();
    addTestResult('✅ Typing indicator started');
    
    setTimeout(() => {
      stopTyping();
      addTestResult('✅ Typing indicator stopped');
    }, 3000);
  };

  // Test presence
  const testPresence = () => {
    setOnline('/test-page');
    addTestResult('✅ Set status to online');
    
    setTimeout(() => {
      setOffline();
      addTestResult('✅ Set status to offline');
    }, 5000);
  };

  useEffect(() => {
    if (isConnected) {
      addTestResult('🔌 Connected to Supabase real-time');
    }
  }, [isConnected]);

  if (!session?.user?.id) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please log in to test real-time features</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Real-time System Test</h2>
        
        {/* Connection Status */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Connection Status</h3>
          <RealtimeStatus showConnectionStatus={true} showOnlineStatus={false} />
          <button
            onClick={testConnection}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Test Connection
          </button>
        </div>

        {/* Message Test */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Message Test</h3>
          <div className="flex space-x-2">
            <input
              type="text"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Enter test message..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={testMessageSend}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Send Test Message
            </button>
          </div>
        </div>

        {/* Notification Test */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Notification Test</h3>
          <button
            onClick={testNotification}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Test Notification
          </button>
        </div>

        {/* Typing Indicator Test */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Typing Indicator Test</h3>
          <button
            onClick={testTyping}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
          >
            Test Typing Indicator
          </button>
          <div className="mt-2">
            <TypingIndicator typingUsers={typingUsers} />
          </div>
        </div>

        {/* Presence Test */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Presence Test</h3>
          <button
            onClick={testPresence}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Test Presence
          </button>
          <div className="mt-2">
            <OnlineUsersList onlineUsers={onlineUsers} />
          </div>
        </div>

        {/* Test Results */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Test Results</h3>
          <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">No test results yet</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
          <button
            onClick={() => setTestResults([])}
            className="mt-2 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {/* System Info */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-2">System Information</h3>
          <div className="text-sm space-y-1">
            <p><strong>User ID:</strong> {session.user.id}</p>
            <p><strong>Connection Status:</strong> {isConnected ? 'Connected' : 'Disconnected'}</p>
            <p><strong>Online Users:</strong> {onlineUsers.length}</p>
            <p><strong>Typing Users:</strong> {typingUsers.length}</p>
            <p><strong>Browser:</strong> {navigator.userAgent.split(' ')[0]}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
