import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { messages, users } from "@/lib/db/schema";
import { eq, and, desc, gt } from "drizzle-orm";

/**
 * GET /api/messages/poll
 * Poll for new messages (fallback for when real-time fails)
 */
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const afterId = searchParams.get('after');
    const conversationWith = searchParams.get('with');

    let query = db.select({
      id: messages.id,
      content: messages.content,
      senderId: messages.senderId,
      receiverId: messages.receiverId,
      createdAt: messages.createdAt,
      sender: {
        id: users.id,
        name: users.name,
        image: users.image,
      },
    })
    .from(messages)
    .leftJoin(users, eq(messages.senderId, users.id))
    .where(
      and(
        // Messages where user is sender or receiver
        conversationWith 
          ? and(
              eq(messages.senderId, conversationWith),
              eq(messages.receiverId, session.user.id)
            )
          : eq(messages.receiverId, session.user.id)
      )
    )
    .orderBy(desc(messages.createdAt))
    .limit(20);

    // If afterId is provided, only get messages after that ID
    if (afterId) {
      const afterMessage = await db.query.messages.findFirst({
        where: eq(messages.id, afterId),
        columns: { createdAt: true },
      });

      if (afterMessage) {
        query = query.where(
          and(
            conversationWith 
              ? and(
                  eq(messages.senderId, conversationWith),
                  eq(messages.receiverId, session.user.id)
                )
              : eq(messages.receiverId, session.user.id),
            gt(messages.createdAt, afterMessage.createdAt)
          )
        );
      }
    }

    const newMessages = await query;

    return NextResponse.json({
      success: true,
      messages: newMessages,
      count: newMessages.length,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Message polling error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to poll messages" },
      { status: 500 }
    );
  }
}
