'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { supabase } from '@/lib/database/hybrid-client';
import toast from 'react-hot-toast';

// Types
interface RealtimeMessage {
  message_id: string;
  notification_id: string;
  content: string;
  sender: {
    id: string;
    name: string;
    image: string;
  };
  created_at: string;
}

interface RealtimeNotification {
  notification_id: string;
  notification_type: string;
  post_id?: string;
  sender: {
    id: string;
    name: string;
    image: string;
  };
  created_at: string;
}

interface RealtimeEvent {
  type: 'new_message' | 'notification' | 'typing' | 'online_status';
  recipient_id: string;
  sender_id: string;
  data: RealtimeMessage | RealtimeNotification | any;
}

/**
 * Main real-time hook for messages and notifications
 */
export const useRealtime = () => {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const channelRef = useRef<any>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Event handlers
  const [onNewMessage, setOnNewMessage] = useState<((data: RealtimeMessage) => void) | null>(null);
  const [onNewNotification, setOnNewNotification] = useState<((data: RealtimeNotification) => void) | null>(null);
  const [onTyping, setOnTyping] = useState<((data: any) => void) | null>(null);
  const [onUserOnline, setOnUserOnline] = useState<((data: any) => void) | null>(null);

  // Connect to real-time
  const connect = useCallback(() => {
    if (!session?.user?.id || channelRef.current) return;

    console.log('🔌 Connecting to real-time...');

    const channel = supabase
      .channel(`user-${session.user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'live_events',
          filter: `recipient_id=eq.${session.user.id}`,
        },
        (payload) => {
          const event = payload.new as RealtimeEvent;
          console.log('📡 Real-time event received:', event.type);

          switch (event.type) {
            case 'new_message':
              if (onNewMessage) {
                onNewMessage(event.data as RealtimeMessage);
              }
              // Show toast notification
              toast.success(`New message from ${event.data.sender?.name || 'Someone'}`);
              break;

            case 'notification':
              if (onNewNotification) {
                onNewNotification(event.data as RealtimeNotification);
              }
              // Show toast notification
              const notifData = event.data as RealtimeNotification;
              toast.success(`${notifData.sender?.name || 'Someone'} ${getNotificationText(notifData.notification_type)}`);
              break;

            case 'typing':
              if (onTyping) {
                onTyping(event.data);
              }
              break;

            case 'online_status':
              if (onUserOnline) {
                onUserOnline(event.data);
              }
              break;
          }
        }
      )
      .subscribe((status) => {
        console.log('📡 Supabase connection status:', status);
        
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          setConnectionError(null);
          console.log('✅ Real-time connected successfully');
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false);
          setConnectionError('Connection failed');
          console.error('❌ Real-time connection failed');
          
          // Retry connection after 5 seconds
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log('🔄 Retrying real-time connection...');
            disconnect();
            connect();
          }, 5000);
        } else if (status === 'CLOSED') {
          setIsConnected(false);
          console.log('🔌 Real-time connection closed');
        }
      });

    channelRef.current = channel;
  }, [session?.user?.id, onNewMessage, onNewNotification, onTyping, onUserOnline]);

  // Disconnect from real-time
  const disconnect = useCallback(() => {
    if (channelRef.current) {
      console.log('🔌 Disconnecting from real-time...');
      supabase.removeChannel(channelRef.current);
      channelRef.current = null;
      setIsConnected(false);
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Auto-connect when session is available
  useEffect(() => {
    if (session?.user?.id) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [session?.user?.id, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    // Event handler setters
    setOnNewMessage,
    setOnNewNotification,
    setOnTyping,
    setOnUserOnline,
  };
};

/**
 * Hook specifically for real-time messages
 */
export const useRealtimeMessages = (conversationUserId?: string) => {
  const { setOnNewMessage } = useRealtime();
  const [messages, setMessages] = useState<any[]>([]);

  const handleNewMessage = useCallback((data: RealtimeMessage) => {
    // Only add message if it's for the current conversation
    if (!conversationUserId || data.sender.id === conversationUserId) {
      setMessages(prev => [...prev, {
        id: data.message_id,
        content: data.content,
        senderId: data.sender.id,
        sender: data.sender,
        createdAt: data.created_at,
      }]);
    }
  }, [conversationUserId]);

  useEffect(() => {
    setOnNewMessage(() => handleNewMessage);
  }, [handleNewMessage, setOnNewMessage]);

  return {
    messages,
    setMessages,
  };
};

/**
 * Hook specifically for real-time notifications
 */
export const useRealtimeNotifications = () => {
  const { setOnNewNotification } = useRealtime();
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  const handleNewNotification = useCallback((data: RealtimeNotification) => {
    setNotifications(prev => [{
      id: data.notification_id,
      type: data.notification_type,
      sender: data.sender,
      postId: data.post_id,
      createdAt: data.created_at,
      read: false,
    }, ...prev]);
    
    setUnreadCount(prev => prev + 1);
  }, []);

  useEffect(() => {
    setOnNewNotification(() => handleNewNotification);
  }, [handleNewNotification, setOnNewNotification]);

  return {
    notifications,
    setNotifications,
    unreadCount,
    setUnreadCount,
  };
};

// Helper function to get notification text
function getNotificationText(type: string): string {
  switch (type) {
    case 'like':
      return 'liked your post';
    case 'comment':
      return 'commented on your post';
    case 'message':
      return 'sent you a message';
    case 'friend_request':
      return 'sent you a friend request';
    case 'friend_accept':
      return 'accepted your friend request';
    default:
      return 'interacted with your content';
  }
}
