-- Supabase Real-time Tables Setup
-- Run this SQL in your Supabase SQL Editor

-- 1. Create live_events table for real-time notifications
CREATE TABLE IF NOT EXISTS live_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  type VARCHAR(50) NOT NULL, -- 'new_message', 'notification', 'typing', 'online_status'
  recipient_id VARCHAR(255) NOT NULL, -- MySQL user ID
  sender_id VARCHAR(255) NOT NULL, -- MySQL user ID
  data JSONB NOT NULL DEFAULT '{}', -- Event specific data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 hour') -- Auto cleanup
);

-- 2. <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_live_events_recipient ON live_events(recipient_id);
CREATE INDEX IF NOT EXISTS idx_live_events_type ON live_events(type);
CREATE INDEX IF NOT EXISTS idx_live_events_created_at ON live_events(created_at);
CREATE INDEX IF NOT EXISTS idx_live_events_expires_at ON live_events(expires_at);

-- 3. Create user_presence table for online status
CREATE TABLE IF NOT EXISTS user_presence (
  user_id VARCHAR(255) PRIMARY KEY, -- MySQL user ID
  is_online BOOLEAN DEFAULT FALSE,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  current_page VARCHAR(255), -- Current page/route
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create typing_indicators table
CREATE TABLE IF NOT EXISTS typing_indicators (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id VARCHAR(255) NOT NULL, -- MySQL conversation/user ID
  user_id VARCHAR(255) NOT NULL, -- MySQL user ID who is typing
  is_typing BOOLEAN DEFAULT TRUE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '10 seconds')
);

-- 5. Create indexes for typing indicators
CREATE INDEX IF NOT EXISTS idx_typing_conversation ON typing_indicators(conversation_id);
CREATE INDEX IF NOT EXISTS idx_typing_user ON typing_indicators(user_id);
CREATE INDEX IF NOT EXISTS idx_typing_expires_at ON typing_indicators(expires_at);

-- 6. Enable Row Level Security (RLS)
ALTER TABLE live_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_indicators ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies for live_events
CREATE POLICY "Users can view their own events" ON live_events
  FOR SELECT USING (recipient_id = current_setting('request.jwt.claims', true)::json->>'sub');

CREATE POLICY "Users can insert events for others" ON live_events
  FOR INSERT WITH CHECK (true); -- Allow inserting events (will be restricted by API)

-- 8. Create RLS policies for user_presence
CREATE POLICY "Users can view all presence" ON user_presence
  FOR SELECT USING (true); -- Everyone can see who's online

CREATE POLICY "Users can update their own presence" ON user_presence
  FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- 9. Create RLS policies for typing_indicators
CREATE POLICY "Users can view typing in their conversations" ON typing_indicators
  FOR SELECT USING (true); -- Allow viewing typing indicators

CREATE POLICY "Users can manage their own typing status" ON typing_indicators
  FOR ALL USING (user_id = current_setting('request.jwt.claims', true)::json->>'sub');

-- 10. Create function to cleanup expired events
CREATE OR REPLACE FUNCTION cleanup_expired_events()
RETURNS void AS $$
BEGIN
  -- Delete expired live_events
  DELETE FROM live_events WHERE expires_at < NOW();
  
  -- Delete expired typing_indicators
  DELETE FROM typing_indicators WHERE expires_at < NOW();
  
  -- Update user presence for inactive users (offline after 5 minutes)
  UPDATE user_presence 
  SET is_online = FALSE 
  WHERE updated_at < (NOW() - INTERVAL '5 minutes') AND is_online = TRUE;
END;
$$ LANGUAGE plpgsql;

-- 11. Create a scheduled job to run cleanup (if pg_cron is available)
-- Note: This might not work on all Supabase plans, but the function is still useful
-- SELECT cron.schedule('cleanup-expired-events', '*/5 * * * *', 'SELECT cleanup_expired_events();');

-- 12. Create function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
  p_user_id VARCHAR(255),
  p_is_online BOOLEAN DEFAULT TRUE,
  p_current_page VARCHAR(255) DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  INSERT INTO user_presence (user_id, is_online, current_page, updated_at)
  VALUES (p_user_id, p_is_online, p_current_page, NOW())
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    is_online = p_is_online,
    current_page = COALESCE(p_current_page, user_presence.current_page),
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 13. Create function to set typing indicator
CREATE OR REPLACE FUNCTION set_typing_indicator(
  p_conversation_id VARCHAR(255),
  p_user_id VARCHAR(255),
  p_is_typing BOOLEAN DEFAULT TRUE
)
RETURNS void AS $$
BEGIN
  IF p_is_typing THEN
    -- Insert or update typing indicator
    INSERT INTO typing_indicators (conversation_id, user_id, is_typing, updated_at, expires_at)
    VALUES (p_conversation_id, p_user_id, TRUE, NOW(), NOW() + INTERVAL '10 seconds')
    ON CONFLICT (conversation_id, user_id) 
    DO UPDATE SET 
      is_typing = TRUE,
      updated_at = NOW(),
      expires_at = NOW() + INTERVAL '10 seconds';
  ELSE
    -- Remove typing indicator
    DELETE FROM typing_indicators 
    WHERE conversation_id = p_conversation_id AND user_id = p_user_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 14. Enable real-time for all tables
ALTER PUBLICATION supabase_realtime ADD TABLE live_events;
ALTER PUBLICATION supabase_realtime ADD TABLE user_presence;
ALTER PUBLICATION supabase_realtime ADD TABLE typing_indicators;

-- 15. Create a view for active typing users
CREATE OR REPLACE VIEW active_typing_users AS
SELECT 
  conversation_id,
  user_id,
  updated_at
FROM typing_indicators 
WHERE is_typing = TRUE AND expires_at > NOW();

-- Success message
SELECT 'Supabase real-time tables created successfully!' as message;
