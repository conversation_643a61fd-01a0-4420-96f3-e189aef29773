import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { RealtimeTest } from "@/components/testing/RealtimeTest";
import { MainLayout } from "@/components/layout/MainLayout";

export default async function TestRealtimePage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/auth/signin");
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Real-time System Test</h1>
            <p className="mt-2 text-gray-600">
              Test the hybrid real-time system with Supabase and MySQL integration.
            </p>
          </div>
          
          <RealtimeTest />
          
          <div className="mt-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">How to Test</h2>
            <div className="space-y-3 text-gray-700">
              <div className="flex items-start space-x-2">
                <span className="font-semibold text-blue-600">1.</span>
                <p>Check if the real-time connection is established (green dot = connected)</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-semibold text-blue-600">2.</span>
                <p>Send a test message to yourself to verify message real-time delivery</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-semibold text-blue-600">3.</span>
                <p>Test notifications by creating and liking a post</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-semibold text-blue-600">4.</span>
                <p>Test typing indicators in a conversation</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-semibold text-blue-600">5.</span>
                <p>Test online/offline presence status</p>
              </div>
            </div>
          </div>

          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-yellow-800 mb-4">Important Notes</h2>
            <div className="space-y-2 text-yellow-700">
              <p>• Make sure you have run the Supabase SQL setup script</p>
              <p>• Check browser console for any connection errors</p>
              <p>• Real-time features work best with multiple browser tabs/users</p>
              <p>• If connection fails, check your Supabase credentials in .env</p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
