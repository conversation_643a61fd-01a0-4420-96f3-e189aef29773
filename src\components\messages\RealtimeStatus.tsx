'use client';

import { useRealtime } from '@/hooks/useRealtime';
import { useUserPresence } from '@/hooks/useTypingIndicator';

interface RealtimeStatusProps {
  userId?: string;
  showConnectionStatus?: boolean;
  showOnlineStatus?: boolean;
  className?: string;
}

export function RealtimeStatus({ 
  userId, 
  showConnectionStatus = true, 
  showOnlineStatus = true,
  className = ""
}: RealtimeStatusProps) {
  const { isConnected, connectionError } = useRealtime();
  const { isUserOnline } = useUserPresence();

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Connection Status */}
      {showConnectionStatus && (
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            isConnected 
              ? 'bg-green-500' 
              : connectionError 
                ? 'bg-red-500' 
                : 'bg-yellow-500'
          }`} />
          <span className="text-xs text-gray-500">
            {isConnected 
              ? 'Connected' 
              : connectionError 
                ? 'Connection failed' 
                : 'Connecting...'}
          </span>
        </div>
      )}

      {/* User Online Status */}
      {showOnlineStatus && userId && (
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            isUserOnline(userId) ? 'bg-green-500' : 'bg-gray-300'
          }`} />
          <span className="text-xs text-gray-500">
            {isUserOnline(userId) ? 'Online' : 'Offline'}
          </span>
        </div>
      )}
    </div>
  );
}

interface TypingIndicatorProps {
  typingUsers: Array<{ user_id: string; updated_at: string }>;
  className?: string;
}

export function TypingIndicator({ typingUsers, className = "" }: TypingIndicatorProps) {
  if (typingUsers.length === 0) return null;

  return (
    <div className={`flex items-center space-x-2 text-sm text-gray-500 ${className}`}>
      <div className="flex space-x-1">
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span>
        {typingUsers.length === 1 
          ? 'Someone is typing...' 
          : `${typingUsers.length} people are typing...`}
      </span>
    </div>
  );
}

interface OnlineUsersListProps {
  onlineUsers: string[];
  className?: string;
}

export function OnlineUsersList({ onlineUsers, className = "" }: OnlineUsersListProps) {
  if (onlineUsers.length === 0) return null;

  return (
    <div className={`${className}`}>
      <h3 className="text-sm font-medium text-gray-700 mb-2">
        Online ({onlineUsers.length})
      </h3>
      <div className="space-y-1">
        {onlineUsers.slice(0, 10).map((userId) => (
          <div key={userId} className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span className="text-sm text-gray-600">User {userId.slice(0, 8)}</span>
          </div>
        ))}
        {onlineUsers.length > 10 && (
          <div className="text-xs text-gray-500">
            +{onlineUsers.length - 10} more online
          </div>
        )}
      </div>
    </div>
  );
}
