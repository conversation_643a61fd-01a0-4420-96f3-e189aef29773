import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { notifications } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

/**
 * POST /api/notifications/mark-read
 * Mark notifications as read
 */
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { notificationId } = body;

    if (notificationId) {
      // Mark single notification as read
      await db
        .update(notifications)
        .set({ read: true })
        .where(
          and(
            eq(notifications.id, notificationId),
            eq(notifications.recipientId, session.user.id)
          )
        );
    } else {
      // Mark all notifications as read
      await db
        .update(notifications)
        .set({ read: true })
        .where(eq(notifications.recipientId, session.user.id));
    }

    return NextResponse.json({
      success: true,
      message: notificationId 
        ? "Notification marked as read" 
        : "All notifications marked as read",
    });

  } catch (error) {
    console.error("Error marking notifications as read:", error);
    return NextResponse.json(
      { success: false, error: "Failed to mark notifications as read" },
      { status: 500 }
    );
  }
}
